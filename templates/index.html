<!DOCTYPE html>
<html lang="zh_TW">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>車牌識別系統</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
        }
        .video-container {
            text-align: center;
            margin: 20px 0;
        }
        #video {
            width: 100%;
            max-width: 640px;
            height: auto;
            border: 2px solid #ddd;
            border-radius: 5px;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            font-size: 18px;
            min-height: 25px;
        }
        .success {
            background-color: #dff0d8;
            color: #3c763d;
            border: 1px solid #d6e9c6;
        }
        .error {
            background-color: #f2dede;
            color: #a94442;
            border: 1px solid #ebccd1;
        }
        .upload-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px dashed #ccc;
            border-radius: 5px;
            text-align: center;
        }
        #fileInput {
            margin: 10px 0;
        }
        .note {
            background-color: #e7f3ff;
            border-left: 6px solid #2196F3;
            padding: 10px;
            margin: 20px 0;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>車牌識別系統</h1>
        
        <div class="note">
            <p><strong>提示：</strong>如果無法訪問攝像頭，請確保：</p>
            <ul>
                <li>攝像頭已正確連線到電腦</li>
                <li>瀏覽器已獲得攝像頭訪問許可權</li>
                <li>沒有其他程式正在使用攝像頭</li>
            </ul>
        </div>
        
        <div class="video-container">
            <img id="video" src="{{ url_for('video_feed') }}" alt="攝像頭畫面" onerror="handleVideoError()">
        </div>
        
        <div class="controls">
            <button id="captureBtn" onclick="captureImage()">拍攝並識別</button>
        </div>
        
        <div id="result" class="result" style="display: none;"></div>
        
        <div class="upload-section">
            <h3>或者上傳圖片進行識別</h3>
            <input type="file" id="fileInput" accept="image/*">
            <button id="uploadBtn" onclick="uploadImage()">上傳並識別</button>
        </div>
    </div>

    <script>
        let isProcessing = false;

        function showResult(message, isSuccess) {
            const resultDiv = document.getElementById('result');
            resultDiv.textContent = message;
            resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
            resultDiv.style.display = 'block';
        }

        function handleVideoError() {
            const videoElement = document.getElementById('video');
            videoElement.alt = "無法訪問攝像頭，請檢查攝像頭連線或許可權設定";
        }

        function captureImage() {
            if (isProcessing) return;
            
            isProcessing = true;
            const captureBtn = document.getElementById('captureBtn');
            captureBtn.disabled = true;
            captureBtn.textContent = '處理中...';
            
            fetch('/capture', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                console.log('拍攝識別響應:', data);  // 除錯資訊
                if (data.success) {
                    showResult('識別結果: ' + data.plate, true);
                } else {
                    showResult('識別失敗: ' + data.error, false);
                }
            })
            .catch(error => {
                console.error('拍攝識別錯誤:', error);  // 除錯資訊
                showResult('請求失敗: ' + error, false);
            })
            .finally(() => {
                isProcessing = false;
                captureBtn.disabled = false;
                captureBtn.textContent = '拍攝並識別';
            });
        }

        function uploadImage() {
            if (isProcessing) return;
            
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                showResult('請選擇一個圖片檔案', false);
                return;
            }
            
            isProcessing = true;
            const uploadBtn = document.getElementById('uploadBtn');
            uploadBtn.disabled = true;
            uploadBtn.textContent = '處理中...';
            
            const formData = new FormData();
            formData.append('image', file);
            
            fetch('/recognize', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('響應狀態:', response.status);  // 除錯資訊
                return response.json();
            })
            .then(data => {
                console.log('上傳識別響應:', data);  // 除錯資訊
                if (data.success) {
                    showResult('識別結果: ' + data.plate, true);
                } else {
                    showResult('識別失敗: ' + data.error, false);
                }
            })
            .catch(error => {
                console.error('上傳識別錯誤:', error);  // 除錯資訊
                showResult('請求失敗: ' + error, false);
            })
            .finally(() => {
                isProcessing = false;
                uploadBtn.disabled = false;
                uploadBtn.textContent = '上傳並識別';
            });
        }

        // 允許使用者通過回車鍵上傳檔案
        document.getElementById('fileInput').addEventListener('change', function(e) {
            if (e.target.files.length > 0) {
                document.getElementById('uploadBtn').focus();
            }
        });
    </script>
</body>
</html>