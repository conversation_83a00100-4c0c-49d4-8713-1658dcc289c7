#!/usr/bin/env python3
"""
测试脚本：验证摄像头修复和图片上传功能
"""

import requests
import cv2
import numpy as np
import json
import io

def test_camera_status():
    """测试摄像头状态接口"""
    print("1. 测试摄像头状态接口...")
    try:
        response = requests.get('http://127.0.0.1:5000/camera_status')
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ 摄像头可用: {data['camera_available']}")
            print(f"   ✓ 测试模式: {data['test_mode']}")
            print(f"   ✓ 消息: {data['message']}")
            return True
        else:
            print(f"   ✗ 请求失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ✗ 请求异常: {e}")
        return False

def test_video_feed():
    """测试视频流接口"""
    print("\n2. 测试视频流接口...")
    try:
        response = requests.get('http://127.0.0.1:5000/video_feed', stream=True, timeout=5)
        if response.status_code == 200:
            print(f"   ✓ 视频流响应正常，Content-Type: {response.headers.get('Content-Type')}")
            return True
        else:
            print(f"   ✗ 视频流请求失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ✗ 视频流请求异常: {e}")
        return False

def test_capture():
    """测试拍摄接口"""
    print("\n3. 测试拍摄接口...")
    try:
        response = requests.post('http://127.0.0.1:5000/capture')
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ 拍摄响应: success={data.get('success')}")
            if not data.get('success'):
                print(f"   ✓ 预期错误消息: {data.get('error')}")
            return True
        else:
            print(f"   ✗ 拍摄请求失败，状态码: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ✗ 拍摄请求异常: {e}")
        return False

def create_test_image():
    """创建一个测试图片"""
    # 创建一个简单的测试图片，包含模拟车牌
    img = np.zeros((300, 400, 3), dtype=np.uint8)
    img.fill(255)  # 白色背景
    
    # 绘制一个模拟车牌区域
    cv2.rectangle(img, (50, 100), (350, 200), (0, 0, 0), 2)
    cv2.rectangle(img, (55, 105), (345, 195), (255, 255, 0), -1)  # 蓝色背景
    
    # 添加模拟车牌文字
    font = cv2.FONT_HERSHEY_SIMPLEX
    cv2.putText(img, 'TEST123', (100, 160), font, 1.5, (0, 0, 0), 3)
    
    # 编码为JPEG
    _, buffer = cv2.imencode('.jpg', img)
    return buffer.tobytes()

def test_image_upload():
    """测试图片上传识别功能"""
    print("\n4. 测试图片上传识别功能...")
    try:
        # 创建测试图片
        test_image_data = create_test_image()
        
        # 准备文件上传
        files = {'image': ('test.jpg', test_image_data, 'image/jpeg')}
        
        response = requests.post('http://127.0.0.1:5000/recognize', files=files, timeout=30)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✓ 上传识别响应: success={data.get('success')}")
            if data.get('success'):
                print(f"   ✓ 识别结果: {data.get('plate')}")
            else:
                print(f"   ✓ 错误信息: {data.get('error')}")
            return True
        else:
            print(f"   ✗ 上传识别请求失败，状态码: {response.status_code}")
            print(f"   ✗ 响应内容: {response.text}")
            return False
    except Exception as e:
        print(f"   ✗ 上传识别请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试车牌识别系统...")
    print("=" * 50)
    
    results = []
    results.append(test_camera_status())
    results.append(test_video_feed())
    results.append(test_capture())
    results.append(test_image_upload())
    
    print("\n" + "=" * 50)
    print("测试结果总结:")
    test_names = ["摄像头状态", "视频流", "拍摄功能", "图片上传"]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{i+1}. {name}: {status}")
    
    passed = sum(results)
    total = len(results)
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
    else:
        print("⚠️  部分测试失败，请检查相关功能。")

if __name__ == "__main__":
    main()
