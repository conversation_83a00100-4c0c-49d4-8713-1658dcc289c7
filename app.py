import cv2
import numpy as np
from PIL import Image
import requests
import json
import os
import base64
from flask import Flask, render_template, request, jsonify, Response
import threading
import time

class PlateRecognizer:
    def __init__(self):
        self.confidence_threshold = 0.8
        self.max_attempts = 3
        self.gemini_api_key = "AIzaSyDLco-Mj9BWgQOEbAhBdZwgKb8jqOHWgpo"
        
    def recognize_plate(self, image_path=None, image_data=None):
        """识别车牌"""
        if image_data:
            # 直接使用传入的图像数据
            result = self.correct_with_gemini("", image_data=image_data)
            return result
        elif image_path:
            # 直接使用Gemini进行识别
            result = self.correct_with_gemini("", image_path)
            return result
        else:
            raise Exception("未提供图像数据")
    
    def encode_image_to_base64(self, image_path=None, image_data=None):
        """将图片编码为base64"""
        if image_data:
            # 如果提供了图像数据，直接编码
            return base64.b64encode(image_data).decode('utf-8')
        elif image_path:
            # 否则从文件路径读取
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        else:
            raise Exception("未提供图像数据或路径")
    
    def preprocess_image(self, image_data):
        """预处理图像以提高识别准确率"""
        # 将字节数据转换为numpy数组
        nparr = np.frombuffer(image_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        
        # 调整图像大小以提高识别效果
        height, width = img.shape[:2]
        if width > 800 or height > 600:
            # 保持宽高比缩放
            scale = min(800/width, 600/height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_AREA)
        
        # 转换为JPEG格式
        _, buffer = cv2.imencode('.jpg', img, [int(cv2.IMWRITE_JPEG_QUALITY), 90])
        return buffer.tobytes()
    
    def correct_with_gemini(self, text, image_path=None, image_data=None):
        """使用Gemini校正识别结果"""
        try:
            # 构建请求数据
            if image_path or image_data:
                # 预处理图像
                if image_data:
                    processed_image_data = self.preprocess_image(image_data)
                    image_base64 = self.encode_image_to_base64(image_data=processed_image_data)
                else:
                    image_base64 = self.encode_image_to_base64(image_path)
                
                # 构建Gemini API请求 - 使用新的模型名称
                url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={self.gemini_api_key}"
                
                payload = {
                    "contents": [
                        {
                            "parts": [
                                {
                                    "text": "请仔细识别这张图片中的车牌号码。请按照以下步骤操作：\n"
                                            "1. 找到图片中的车牌区域\n"
                                            "2. 识别车牌上的字符\n"
                                            "3. 只返回识别出的车牌号码，不要包含任何其他文字或解释\n"
                                            "4. 如果无法识别，请返回'未识别到车牌'\n\n"
                                            "车牌号码通常由汉字、英文字母和数字组成，例如'粤A12345'或'ABC-1234'。"
                                },
                                {
                                    "inline_data": {
                                        "mime_type": "image/jpeg",
                                        "data": image_base64
                                    }
                                }
                            ]
                        }
                    ]
                }
            else:
                # 仅文本校正
                url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={self.gemini_api_key}"
                
                payload = {
                    "contents": [
                        {
                            "parts": [
                                {
                                    "text": f"请校正以下车牌号码识别结果，确保符合中国车牌格式规范：{text}。只返回校正后的车牌号码，不要有其他文字。"
                                }
                            ]
                        }
                    ]
                }
            
            # 调用Gemini API
            response = requests.post(
                url,
                headers={"Content-Type": "application/json"},
                data=json.dumps(payload),
                verify=True,
                timeout=30  # 添加超时设置
            )
            
            # 打印响应内容用于调试
            print(f"Gemini API响应状态: {response.status_code}")
            
            result = response.json()
            print(f"Gemini API响应内容: {result}")  # 调试信息
            
            if 'candidates' in result and len(result['candidates']) > 0:
                candidate = result['candidates'][0]
                if 'content' in candidate and 'parts' in candidate['content']:
                    corrected = candidate['content']['parts'][0]['text'].strip()
                    # 检查是否包含非"未识别到车牌"的有效内容
                    if corrected and corrected != "未识别到车牌":
                        return corrected
                    else:
                        return "未识别到车牌"
            
            # 如果没有有效结果，返回空字符串而不是text
            return "未识别到车牌"
            
        except requests.exceptions.RequestException as e:
            print(f"Gemini API请求失败: {e}")
            raise Exception(f"API请求失败: {str(e)}")
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            raise Exception("API响应格式错误")
        except Exception as e:
            print(f"Gemini校正失败: {e}")
            raise Exception(f"识别失败: {str(e)}")

# 全局变量用于存储摄像头帧
camera = None
current_frame = None
camera_lock = threading.Lock()
camera_available = False
test_mode = False

def check_camera_availability():
    """检查摄像头是否可用"""
    global camera_available
    try:
        test_cap = cv2.VideoCapture(0)
        camera_available = test_cap.isOpened()
        test_cap.release()
        return camera_available
    except Exception as e:
        print(f"摄像头检查失败: {e}")
        camera_available = False
        return False

def create_placeholder_frame():
    """创建占位符图像"""
    # 创建一个640x480的黑色图像，带有文字提示
    img = np.zeros((480, 640, 3), dtype=np.uint8)

    # 添加文字提示
    font = cv2.FONT_HERSHEY_SIMPLEX
    text1 = "No Camera Available"
    text2 = "Please upload an image"
    text3 = "to recognize license plate"

    # 计算文字位置（居中）
    text_size1 = cv2.getTextSize(text1, font, 1, 2)[0]
    text_size2 = cv2.getTextSize(text2, font, 0.7, 2)[0]
    text_size3 = cv2.getTextSize(text3, font, 0.7, 2)[0]

    x1 = (640 - text_size1[0]) // 2
    x2 = (640 - text_size2[0]) // 2
    x3 = (640 - text_size3[0]) // 2

    cv2.putText(img, text1, (x1, 200), font, 1, (255, 255, 255), 2)
    cv2.putText(img, text2, (x2, 250), font, 0.7, (200, 200, 200), 2)
    cv2.putText(img, text3, (x3, 280), font, 0.7, (200, 200, 200), 2)

    return img

def get_frame():
    global camera, current_frame, camera_available, test_mode
    with camera_lock:
        # 如果摄像头不可用，返回占位符图像
        if not camera_available:
            if not test_mode:
                # 首次检查摄像头可用性
                print("正在检查摄像头可用性...")
                if not check_camera_availability():
                    print("警告：未检测到可用的摄像头设备")
                    print("将使用占位符图像。您仍可以通过上传图片进行车牌识别。")
                    test_mode = True

            if test_mode:
                placeholder_frame = create_placeholder_frame()
                current_frame = placeholder_frame
                ret, buffer = cv2.imencode('.jpg', placeholder_frame)
                if ret:
                    return buffer.tobytes()
                return None

        # 尝试使用真实摄像头
        if camera is None:
            print("正在尝试连接摄像头...")
            camera = cv2.VideoCapture(0)
            if not camera.isOpened():
                print("错误：无法打开摄像头")
                camera_available = False
                test_mode = True
                return get_frame()  # 递归调用以获取占位符图像

        ret, frame = camera.read()
        if ret:
            current_frame = frame
            ret, buffer = cv2.imencode('.jpg', frame)
            if ret:
                return buffer.tobytes()
        else:
            print("错误：无法从摄像头读取画面")
            # 摄像头可能断开连接，重置状态
            camera.release()
            camera = None
            camera_available = False
            test_mode = True
    return None

def release_camera():
    global camera
    with camera_lock:
        if camera is not None:
            camera.release()
            camera = None
            print("摄像头已释放")

def generate_frames():
    while True:
        frame = get_frame()
        if frame:
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + frame + b'\r\n')
        else:
            # 如果无法获取摄像头画面，等待一段时间再尝试
            time.sleep(1)
            # 返回空的JPEG数据，保持流连接
            empty_frame = b'\xff\xd8\xff\xe0\x00\x10JFIF\x00\x01\x01\x01\x00H\x00H\x00\x00\xff\xdb\x00C\x00\x08\x06\x06\x07\x06\x05\x08\x07\x07\x07\t\t\x08\n\x0c\x14\r\x0c\x0b\x0b\x0c\x19\x12\x13\x0f\x14\x1d\x1a\x1f\x1e\x1d\x1a\x1c\x1c $.\' ",#\x1c\x1c(7),01444\x1f\'9=82<.342\xff\xdb\x00C\x01\t\t\t\x0c\x0b\x0c\x18\x0d\x0d\x182!\x1c!22222222222222222222222222222222222222222222222222\xff\xc0\x00\x11\x08\x00\x01\x00\x01\x01\x01\x11\x00\xff\xc4\x00\x1f\x00\x00\x01\x05\x01\x01\x01\x01\x01\x01\x00\x00\x00\x00\x00\x00\x00\x00\x01\x02\x03\x04\x05\x06\x07\xff\xc4\x00\xb5\x10\x00\x02\x01\x03\x03\x02\x04\x03\x05\x05\x04\x04\x00\x00\x01\x7d\x01\x02\x03\x00\x04\x11\x05\x12!1\x13\x06\x14Q\x15\x16"q\x17\x182\x81\x91\xa1\x07\x19\xb1\xc1\x08\x1a\xd1\xe1\xf0\x1b\xf1\x22\x23\x24\x25\x26\x27\x28\x29\x2a\x32\x33\x34\x35\x36\x37\x38\x39\x3a\x42\x43\x44\x45\x46\x47\x48\x49\x4a\x52\x53\x54\x55\x56\x57\x58\x59\x5a\x62\x63\x64\x65\x66\x67\x68\x69\x6a\x72\x73\x74\x75\x76\x77\x78\x79\x7a\x82\x83\x84\x85\x86\x87\x88\x89\x8a\x92\x93\x94\x95\x96\x97\x98\x99\x9a\xa2\xa3\xa4\xa5\xa6\xa7\xa8\xa9\xaa\xb2\xb3\xb4\xb5\xb6\xb7\xb8\xb9\xba\xc2\xc3\xc4\xc5\xc6\xc7\xc8\xc9\xca\xd2\xd3\xd4\xd5\xd6\xd7\xd8\xd9\xda\xe2\xe3\xe4\xe5\xe6\xe7\xe8\xe9\xea\xf2\xf3\xf4\xf5\xf6\xf7\xf8\xf9\xfa\xff\xda\x00\x0c\x03\x01\x00\x02\x11\x03\x11\x00?\x00\xff\xd9'
            yield (b'--frame\r\n'
                   b'Content-Type: image/jpeg\r\n\r\n' + empty_frame + b'\r\n')

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/video_feed')
def video_feed():
    return Response(generate_frames(),
                    mimetype='multipart/x-mixed-replace; boundary=frame')

@app.route('/capture', methods=['POST'])
def capture():
    global current_frame, test_mode
    if current_frame is not None:
        # 检查是否为测试模式（占位符图像）
        if test_mode:
            return jsonify({
                'success': False,
                'error': '当前没有可用的摄像头。请使用"上传图片"功能进行车牌识别。'
            })

        # 将当前帧保存为临时文件
        _, buffer = cv2.imencode('.jpg', current_frame)
        image_data = buffer.tobytes()

        # 使用车牌识别器识别
        recognizer = PlateRecognizer()
        try:
            result = recognizer.recognize_plate(image_data=image_data)
            # 确保即使结果为空也返回成功状态
            return jsonify({'success': True, 'plate': result if result else '未识别到车牌'})
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)})
    else:
        return jsonify({'success': False, 'error': '无法获取摄像头画面，请检查摄像头是否连接正常'})

@app.route('/camera_status')
def camera_status():
    """返回摄像头状态"""
    global camera_available, test_mode
    return jsonify({
        'camera_available': camera_available,
        'test_mode': test_mode,
        'message': '摄像头可用' if camera_available else '摄像头不可用，请使用上传图片功能'
    })

@app.route('/recognize', methods=['POST'])
def recognize():
    if 'image' not in request.files:
        return jsonify({'success': False, 'error': '未提供图像文件'})

    file = request.files['image']
    if file.filename == '':
        return jsonify({'success': False, 'error': '未选择文件'})

    if file:
        # 读取图像数据
        image_data = file.read()
        print(f"接收到图像数据，大小: {len(image_data)} bytes")  # 调试信息

        # 使用车牌识别器识别
        recognizer = PlateRecognizer()
        try:
            result = recognizer.recognize_plate(image_data=image_data)
            # 确保即使结果为空也返回成功状态
            return jsonify({'success': True, 'plate': result if result else '未识别到车牌'})
        except Exception as e:
            print(f"识别过程中出现异常: {e}")  # 调试信息
            return jsonify({'success': False, 'error': str(e)})

if __name__ == '__main__':
    try:
        # 启动时检查摄像头可用性
        print("正在初始化应用...")
        check_camera_availability()
        if camera_available:
            print("✓ 摄像头可用")
        else:
            print("⚠ 摄像头不可用，将使用占位符模式")
            print("  您仍可以通过上传图片进行车牌识别")

        print("启动Flask应用...")
        app.run(debug=True, host='0.0.0.0', port=5000)
    finally:
        release_camera()